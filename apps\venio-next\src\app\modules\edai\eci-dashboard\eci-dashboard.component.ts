import { Component, OnInit, inject, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { combineLatest, filter, Subject, takeUntil } from 'rxjs'
import { CommonModule } from '@angular/common'
import { ButtonComponent } from '@progress/kendo-angular-buttons'
import { PopupComponent } from '@progress/kendo-angular-popup'
import {
  ListViewComponent,
  ItemTemplateDirective,
} from '@progress/kendo-angular-listview'
import { filterIcon, SVGIcon } from '@progress/kendo-svg-icons'
import { toSignal } from '@angular/core/rxjs-interop'
import { CheckboxListItemComponent } from './shared/checkbox-list-item/checkbox-list-item.component'
import { SummaryComponent } from './shared/summary/summary.component'
import { RelevanceComponent } from './shared/relevance/relevance.component'
import { SunburstComponent } from './shared/sunburst/sunburst.component'
import { InappropriateContentComponent } from './shared/inappropriate-content/inappropriate-content.component'
import { WordCloudComponent } from './shared/word-cloud/word-cloud.component'
import { FocusedSectionComponent } from './shared/focused-section/focused-section.component'
import { AiFacade, ECADashboardType, ECADashboardRequestModel } from '@venio/data-access/ai'
import { DocumentsFacade, SearchFacade } from '@venio/data-access/review'

@Component({
  selector: 'venio-eci-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    PopupComponent,
    ListViewComponent,
    ItemTemplateDirective,
    SummaryComponent,
    CheckboxListItemComponent,
    RelevanceComponent,
    SunburstComponent,
    InappropriateContentComponent,
    WordCloudComponent,
    FocusedSectionComponent,
  ],
  providers: [],
  templateUrl: './eci-dashboard.component.html',
  styleUrls: ['./eci-dashboard.component.scss'],
})
export class EciDashboardComponent implements OnInit, OnDestroy {
  private readonly aiFacade = inject(AiFacade)
  private readonly activatedRoute = inject(ActivatedRoute)
  private readonly documentFacade = inject(DocumentsFacade)
  private readonly searchFacade = inject(SearchFacade)
  private readonly toDestroy$ = new Subject<void>()

  private get projectId(): number {
    return +this.activatedRoute.snapshot.queryParams['projectId']
  }

  // Convert store observables to signals
  public readonly isFocusedSectionOpened = toSignal(
    this.aiFacade.selectEciIsFocusedSectionOpened$,
    { initialValue: false }
  )

  public readonly custodians = toSignal(this.aiFacade.selectEciCustodians$, {
    initialValue: [],
  })

  public readonly showFilterPopup = toSignal(
    this.aiFacade.selectEciShowFilterPopup$,
    { initialValue: false }
  )

  public readonly showCustodianFilters = toSignal(
    this.aiFacade.selectEciShowCustodianFilters$,
    { initialValue: false }
  )

  public readonly svgFilter: SVGIcon = filterIcon

  ngOnInit() {
    // Fetch dashboard data on initialization with projectId
    this.aiFacade.fetchEciDashboardData(this.projectId)

    // Subscribe to search and document data to make ECA API calls
    this.#subscribeToSearchData()
  }

  ngOnDestroy() {
    this.toDestroy$.next()
    this.toDestroy$.complete()
  }

  #subscribeToSearchData(): void {
    combineLatest([
      this.searchFacade.getSearchResponse$,
      this.documentFacade.getSelectedDocuments$,
      this.documentFacade.getUnselectedDocuments$,
      this.documentFacade.getIsBatchSelected$,
    ])
      .pipe(
        filter(([response]) => Boolean(response)),
        takeUntil(this.toDestroy$)
      )
      .subscribe(([searchResponse, selectedFileIds, unSelectedFileIds, isBatchSelected]) => {
        const {
          tempTables: { searchResultTempTable },
        } = searchResponse

        console.log('ECI Dashboard: Making ECA API calls with data:', {
          searchResultTempTable,
          selectedFileIds: selectedFileIds.length,
          unSelectedFileIds: unSelectedFileIds.length,
          isBatchSelected,
          projectId: this.projectId
        })

        // Make ECA API calls for different dashboard types
        this.#fetchEcaData(searchResultTempTable, selectedFileIds, unSelectedFileIds, isBatchSelected)
      })
  }

  #fetchEcaData(
    searchTempTable: string,
    selectedFileIds: number[],
    unSelectedFileIds: number[],
    isBatchSelection: boolean
  ): void {
    // Fetch Relevance data
    const relevanceRequest = this.aiFacade.buildEcaRequestModel(
      ECADashboardType.Relevance,
      searchTempTable,
      selectedFileIds,
      unSelectedFileIds,
      isBatchSelection
    )
    this.aiFacade.fetchEcaRelevance(this.projectId, relevanceRequest)

    // Fetch Document Types data
    const documentTypesRequest = this.aiFacade.buildEcaRequestModel(
      ECADashboardType.DocumentType,
      searchTempTable,
      selectedFileIds,
      unSelectedFileIds,
      isBatchSelection
    )
    this.aiFacade.fetchEcaDocumentTypes(this.projectId, documentTypesRequest)

    // Fetch Topics data (both relevant and non-relevant)
    const topicsRelevantRequest = this.aiFacade.buildEcaRequestModel(
      ECADashboardType.Topic_Relevant,
      searchTempTable,
      selectedFileIds,
      unSelectedFileIds,
      isBatchSelection
    )
    this.aiFacade.fetchEcaTopicsRelevant(this.projectId, topicsRelevantRequest)

    const topicsNonRelevantRequest = this.aiFacade.buildEcaRequestModel(
      ECADashboardType.Topic_NonRelevant,
      searchTempTable,
      selectedFileIds,
      unSelectedFileIds,
      isBatchSelection
    )
    this.aiFacade.fetchEcaTopicsNonRelevant(this.projectId, topicsNonRelevantRequest)
  }

  public onToggle(): void {
    this.aiFacade.setEciFilterPopupVisibility(!this.showFilterPopup())
  }

  public onCustodianClick(): void {
    this.aiFacade.setEciCustodianFiltersVisibility(!this.showCustodianFilters())
  }
}
