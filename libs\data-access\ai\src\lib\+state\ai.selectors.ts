import {
  createFeatureSelector,
  createSelector,
  MemoizedSelector,
} from '@ngrx/store'
import { AIState, AI_FEATURE_KEY } from './ai.reducer'

export const getAiState = createFeatureSelector<AIState>(AI_FEATURE_KEY)

export const getStateFromAiStore = <T extends keyof AIState>(
  stateKey: T
): MemoizedSelector<object, AIState[T], unknown> =>
  createSelector(getAiState, (state: AIState) => state[stateKey])

// ECI Dashboard Selectors
export const getEciDashboardState = createSelector(
  getAiState,
  (state: AIState) => state.eciDashboard
)

// UI State Selectors
export const getEciIsFocusedSectionOpened = createSelector(
  getEciDashboardState,
  (eciState) => eciState.isFocusedSectionOpened
)

export const getEciIsParentData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.isParentData
)

export const getEciShowDetails = createSelector(
  getEciDashboardState,
  (eciState) => eciState.showDetails
)

export const getEciShowFilterPopup = createSelector(
  getEciDashboardState,
  (eciState) => eciState.showFilterPopup
)

export const getEciShowCustodianFilters = createSelector(
  getEciDashboardState,
  (eciState) => eciState.showCustodianFilters
)

// Data Selectors
export const getEciCustodians = createSelector(
  getEciDashboardState,
  (eciState) => eciState.custodians
)

export const getEciDocumentTypes = createSelector(
  getEciDashboardState,
  (eciState) => eciState.documentTypes
)

export const getEciFileData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.fileData
)

export const getEciWordCloudData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.wordCloudData
)

export const getEciRelevanceData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.relevanceData
)

export const getEciBarChartData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.barChartData
)

export const getEciTableData = createSelector(
  getEciDashboardState,
  (eciState) => eciState.tableData
)

export const getEciSelectedDocuments = createSelector(
  getEciDashboardState,
  (eciState) => eciState.selectedDocuments
)

export const getEciTotalDocuments = createSelector(
  getEciDashboardState,
  (eciState) => eciState.totalDocuments
)

export const getEciPaginationState = createSelector(
  getEciDashboardState,
  (eciState) => eciState.paginationState
)

// Computed Selectors
export const getEciPagedFileData = createSelector(
  getEciFileData,
  getEciPaginationState,
  (fileData, paginationState) => {
    const startIndex =
      (paginationState.currentPage - 1) * paginationState.pageSize
    const endIndex = startIndex + paginationState.pageSize
    return fileData.slice(startIndex, endIndex)
  }
)

export const getEciSortedDocumentTypes = createSelector(
  getEciDocumentTypes,
  (documentTypes) => [...documentTypes].sort((a, b) => b.count - a.count)
)

// ECA API Selectors
export const getEcaRelevanceSuccess = createSelector(
  getAiState,
  (state: AIState) => state.ecaRelevanceSuccess
)

export const getEcaRelevanceError = createSelector(
  getAiState,
  (state: AIState) => state.ecaRelevanceError
)

export const getIsEcaRelevanceLoading = createSelector(
  getAiState,
  (state: AIState) => state.isEcaRelevanceLoading
)

export const getEcaDocumentTypesSuccess = createSelector(
  getAiState,
  (state: AIState) => state.ecaDocumentTypesSuccess
)

export const getEcaDocumentTypesError = createSelector(
  getAiState,
  (state: AIState) => state.ecaDocumentTypesError
)

export const getIsEcaDocumentTypesLoading = createSelector(
  getAiState,
  (state: AIState) => state.isEcaDocumentTypesLoading
)

export const getEcaTopicsSuccess = createSelector(
  getAiState,
  (state: AIState) => state.ecaTopicsSuccess
)

export const getEcaTopicsError = createSelector(
  getAiState,
  (state: AIState) => state.ecaTopicsError
)

export const getIsEcaTopicsLoading = createSelector(
  getAiState,
  (state: AIState) => state.isEcaTopicsLoading
)
