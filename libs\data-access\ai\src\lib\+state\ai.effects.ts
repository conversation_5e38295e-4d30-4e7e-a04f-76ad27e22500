import { Injectable } from '@angular/core'
import { Actions, createEffect, ofType } from '@ngrx/effects'
import { fetch } from '@ngrx/router-store/data-persistence'
import { from, map, switchMap } from 'rxjs'
import * as AiActions from './ai.actions'
import { HttpErrorResponse } from '@angular/common/http'
import { AiSearchService } from '../services/ai-search.service'
import { EciDashboardService } from '../services/eci-dashboard.service'
import { ResponseModel } from '@venio/shared/models/interfaces'
import { CaseConvertorService } from '@venio/util/utilities'

@Injectable()
export class AiEffects {
  public performAiSearchGroup$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.performAiSearch),
      fetch({
        run: ({ payload, clientId, uuid }) => {
          return this.aiSearchService.performAiSearch(payload, clientId).pipe(
            map((aiSearchSuccess) =>
              AiActions.performAiSearchSuccess({
                aiSearchSuccess,
                uuid,
              })
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const aiSearchError = error.error
          return AiActions.performAiSearchFailure({
            aiSearchError,
            uuid: action.uuid,
          })
        },
      })
    )
  )

  public createJobsEdai$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.createJobEdai),
      fetch({
        run: ({ projectId, payload }) => {
          return this.aiSearchService.eDaiCreateJob(projectId, payload).pipe(
            map((createJobEdaiSuccess) =>
              AiActions.createJobEdaiSuccess({
                createJobEdaiSuccess,
              })
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const createJobEdaiError = error.error
          return AiActions.createJobEdaiFailure({
            createJobEdaiError,
          })
        },
      })
    )
  )

  public fetchEdaiRelevanceCompletedJobs$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEdaiRelevanceCompletedJobs),
      fetch({
        run: ({ projectId }) => {
          return this.aiSearchService
            .fetchEdaiRelevanceCompletedJobs(projectId)
            .pipe(
              map((edaiStatus) =>
                AiActions.fetchEdaiRelevanceCompletedJobsSuccess({
                  edaiStatus,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const edaiStatusError = error.error
          return AiActions.fetchEdaiRelevanceCompletedJobsFailure({
            edaiStatusError,
          })
        },
      })
    )
  )

  public fetchEdaiStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEdaiStatus),
      fetch({
        run: ({ projectId, filterCriteria }) => {
          return this.aiSearchService
            .fetchEdaiStatuses(projectId, filterCriteria)
            .pipe(
              map((edaiStatus) =>
                AiActions.fetchEdaiStatusSuccess({
                  edaiStatus,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const edaiStatusError = error.error
          return AiActions.fetchEdaiStatusFailure({
            edaiStatusError,
          })
        },
      })
    )
  )

  public fetchEdaiDocumentRelevancy$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEdaiDocumentRelevancy),
      fetch({
        run: ({ projectId, fileId }) => {
          return this.aiSearchService
            .fetchEdaiDocumentRelevancy(projectId, fileId)
            .pipe(
              map((edaiDocumentRelevancySuccess) =>
                AiActions.fetchEdaiDocumentRelevancySuccess({
                  edaiDocumentRelevancySuccess,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const edaiDocumentRelevancyError = error.error
          return AiActions.fetchEdaiDocumentRelevancyFailure({
            edaiDocumentRelevancyError,
          })
        },
      })
    )
  )

  public fetchEdaiDocumentPrivilege$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEdaiDocumentPrivilege),
      fetch({
        run: ({ projectId, fileId }) => {
          return this.aiSearchService
            .fetchEdaiDocumentPrivilege(projectId, fileId)
            .pipe(
              map((edaiDocumentPrivilegeSuccess) =>
                AiActions.fetchEdaiDocumentPrivilegeSuccess({
                  edaiDocumentPrivilegeSuccess,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const edaiDocumentPrivilegeError = error.error as ResponseModel
          return AiActions.fetchEdaiDocumentPrivilegeFailure({
            edaiDocumentPrivilegeError,
          })
        },
      })
    )
  )

  public fetchJobStatusDetails$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchJobStatusDetails),
      fetch({
        run: ({ projectId, jobId, jobType }) => {
          return this.aiSearchService
            .fetchJobStatusDetails(projectId, jobId, jobType)
            .pipe(
              map((edaiJobStatusDetailsSuccess) =>
                AiActions.fetchJobStatusDetailsSuccess({
                  edaiJobStatusDetailsSuccess,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const edaiJobStatusDetailsError = error.error
          return AiActions.fetchJobStatusDetailsFailure({
            edaiJobStatusDetailsError,
          })
        },
      })
    )
  )

  public fetchPiiTemplates$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEdaiPiiTemplate),
      fetch({
        run: ({ projectId }) => {
          return this.aiSearchService.fetchPiiTemplates(projectId).pipe(
            switchMap((response) => {
              const camelCaseConvertorService = new CaseConvertorService()
              const convertedData =
                camelCaseConvertorService.convertToCase<any>(
                  response,
                  'camelCase'
                )
              return from(convertedData)
            }),
            map((edaiPIITemplateSuccess) =>
              AiActions.fetchEdaiPiiTemplateSuccess({
                edaiPIITemplateSuccess,
              })
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const edaiPIITemplateError = error.error as ResponseModel
          return AiActions.fetchEdaiPiiTemplateFailure({
            edaiPIITemplateError,
          })
        },
      })
    )
  )

  public fetchPiiEntities$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEdaiPiiEntities),
      fetch({
        run: ({ projectId }) => {
          return this.aiSearchService.fetchPiiEntities(projectId).pipe(
            switchMap((response) => {
              const camelCaseConvertorService = new CaseConvertorService()
              const convertedData =
                camelCaseConvertorService.convertToCase<any>(
                  response,
                  'camelCase'
                )
              return from(convertedData)
            }),
            map((edaiPIIEntitySuccess) =>
              AiActions.fetchEdaiPiiEntitiesSuccess({
                edaiPIIEntitySuccess,
              })
            )
          )
        },
        onError: (action, error: HttpErrorResponse) => {
          const edaiPIIEntityError = error.error as ResponseModel
          return AiActions.fetchEdaiPiiEntitiesFailure({
            edaiPIIEntityError,
          })
        },
      })
    )
  )

  public fetchEdaiDocumentPii$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEdaiDocumentPii),
      fetch({
        run: ({ projectId, fileId, isPiiDetect }) => {
          return this.aiSearchService
            .fetchEdaiDocumentPii(projectId, fileId, isPiiDetect)
            .pipe(
              map((edaiDocumentPIISuccess) =>
                AiActions.fetchEdaiDocumentPiiSuccess({
                  edaiDocumentPIISuccess,
                  isPiiDetect,
                })
              )
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const edaiDocumentPIIError = error.error as ResponseModel
          return AiActions.fetchEdaiDocumentPiiFailure({
            edaiDocumentPIIError,
            isPiiDetect: action.isPiiDetect,
          })
        },
      })
    )
  )

  // ECI Dashboard Effects
  public fetchEciDashboardData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEciDashboardData),
      fetch({
        run: (action) => {
          return this.eciDashboardService
            .loadDashboardData(action.projectId, action.jobId, action.filters)
            .pipe(map((data) => AiActions.fetchEciDashboardDataSuccess(data)))
        },
        onError: (action, error) => {
          return AiActions.fetchEciDashboardDataFailure({ error })
        },
      })
    )
  )

  // ECA API Effects
  public fetchEcaRelevance$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEcaRelevance),
      fetch({
        run: ({ projectId, requestModel }) => {
          return this.eciDashboardService
            .fetchEcaRelevance(projectId, requestModel)
            .pipe(
              map((ecaRelevanceSuccess) => {
                console.log('ECA Relevance Response:', ecaRelevanceSuccess)
                return AiActions.fetchEcaRelevanceSuccess({
                  ecaRelevanceSuccess,
                })
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const ecaRelevanceError = error.error as ResponseModel
          return AiActions.fetchEcaRelevanceFailure({ ecaRelevanceError })
        },
      })
    )
  )

  public fetchEcaDocumentTypes$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEcaDocumentTypes),
      fetch({
        run: ({ projectId, requestModel }) => {
          return this.eciDashboardService
            .fetchEcaDocumentTypes(projectId, requestModel)
            .pipe(
              map((ecaDocumentTypesSuccess) => {
                console.log(
                  'ECA Document Types Response:',
                  ecaDocumentTypesSuccess
                )
                return AiActions.fetchEcaDocumentTypesSuccess({
                  ecaDocumentTypesSuccess,
                })
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const ecaDocumentTypesError = error.error as ResponseModel
          return AiActions.fetchEcaDocumentTypesFailure({
            ecaDocumentTypesError,
          })
        },
      })
    )
  )

  public fetchEcaTopics$ = createEffect(() =>
    this.actions$.pipe(
      ofType(AiActions.fetchEcaTopics),
      fetch({
        run: ({ projectId, requestModel }) => {
          return this.eciDashboardService
            .fetchEcaTopics(projectId, requestModel)
            .pipe(
              map((ecaTopicsSuccess) => {
                console.log('ECA Topics Response:', ecaTopicsSuccess)
                return AiActions.fetchEcaTopicsSuccess({ ecaTopicsSuccess })
              })
            )
        },
        onError: (action, error: HttpErrorResponse) => {
          const ecaTopicsError = error.error as ResponseModel
          return AiActions.fetchEcaTopicsFailure({ ecaTopicsError })
        },
      })
    )
  )

  constructor(
    private readonly actions$: Actions,
    private aiSearchService: AiSearchService,
    private eciDashboardService: EciDashboardService
  ) {}
}
