import { Component, inject } from '@angular/core'
import { PlotlyModule } from 'angular-plotly.js'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'
import { CenterTextComponent } from '../center-text/center-text.component'
import { NgFor } from '@angular/common'
import { LoaderModule } from '@progress/kendo-angular-indicators'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade } from '@venio/data-access/ai'

@Component({
  selector: 'venio-relevance',
  standalone: true,
  imports: [
    PlotlyModule,
    TitleAndDownloadComponent,
    CenterTextComponent,
    NgFor,
    LoaderModule,
  ],
  templateUrl: './relevance.component.html',
  styleUrl: './relevance.component.scss',
})
export class RelevanceComponent {
  private readonly aiFacade = inject(AiFacade)

  public readonly relevanceData = toSignal(
    this.aiFacade.selectEciRelevanceData$,
    { initialValue: null }
  )

  // ECA API data
  public readonly ecaRelevanceData = toSignal(
    this.aiFacade.selectEcaRelevanceSuccess$,
    { initialValue: null }
  )

  public readonly isEcaRelevanceLoading = toSignal(
    this.aiFacade.selectIsEcaRelevanceLoading$,
    { initialValue: false }
  )

  public readonly ecaRelevanceError = toSignal(
    this.aiFacade.selectEcaRelevanceError$,
    { initialValue: null }
  )

  public readonly sunburstChartColors = [
    'transparent',
    '#6305FF',
    '#0084FF',
    '#FF00FF',
    '#1100FF',
    '#0FE5B7',
    '#00D0FF',
  ]

  getColor(i: number): string {
    return this.sunburstChartColors[i + 1]
  }

  get labels(): string[] {
    // Use ECA API data if available, otherwise fallback to mock data
    const ecaData = this.ecaRelevanceData()
    if (ecaData?.data) {
      return ecaData.data.map((item: any) => item.relevanceType)
    }
    return this.relevanceData()?.labels || []
  }

  get graph() {
    // Use ECA API data if available, otherwise fallback to mock data
    const ecaData = this.ecaRelevanceData()
    let values: number[] = []
    let colors: string[] = []

    if (ecaData?.data) {
      values = ecaData.data.map((item: any) => item.docCount)
      // Generate colors for ECA data (you can customize this)
      colors = this.#generateColors(values.length)
    } else {
      const data = this.relevanceData()
      values = data?.values || []
      colors = data?.colors || []
    }

    return {
      data: [
        {
          values,
          type: 'pie',
          hole: 0.5,
          marker: { colors },
          textinfo: 'none',
        },
      ],
      layout: {
        autosize: true,
        title: '',
        automargin: true,
        margin: { t: 0, r: 0, b: 20, l: 20 },
        showlegend: false,
        plot_bgcolor: 'rgba(0,0,0,0)',
        paper_bgcolor: 'rgba(0,0,0,0)',
      },
    }
  }

  #generateColors(count: number): string[] {
    const defaultColors = [
      '#ff00ff',
      '#6305ff',
      '#0084ff',
      '#1100ff',
      '#0fe5b7',
      '#00d0ff',
    ]

    // Repeat colors if we need more than the default set
    const colors: string[] = []
    for (let i = 0; i < count; i++) {
      colors.push(defaultColors[i % defaultColors.length])
    }
    return colors
  }

  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'toImage',
      'sendDataToCloud',
      'editInChartStudio',
      'zoom2d',
      'select2d',
      'pan2d',
      'lasso2d',
      'autoScale2d',
      'resetScale2d',
    ],
  }
}
