import { Component, inject } from '@angular/core'
import { sunburstAnnotationColors } from '../../constants/colors'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>F<PERSON> } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade } from '@venio/data-access/ai'
import { map } from 'rxjs'

@Component({
  selector: 'venio-vertical-legends',
  standalone: true,
  imports: [NgStyle, NgFor],
  templateUrl: './vertical-legends.component.html',
  styleUrl: './vertical-legends.component.scss',
})
export class VerticalLegendsComponent {
  private readonly aiFacade = inject(AiFacade)

  public readonly legends = toSignal(
    this.aiFacade.selectEciDocumentTypes$.pipe(
      map((documentTypes) => documentTypes.map((docuType) => docuType.category))
    ),
    { initialValue: [] }
  )

  public readonly chartColors: string[] = sunburstAnnotationColors
}
