import { Component, inject, Input } from '@angular/core'
import { sunburstAnnotationColors } from '../../constants/colors'
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>F<PERSON> } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade, SunburstChartType } from '@venio/data-access/ai'
import { map, combineLatest } from 'rxjs'

@Component({
  selector: 'venio-vertical-legends',
  standalone: true,
  imports: [Ng<PERSON>tyle, NgFor],
  templateUrl: './vertical-legends.component.html',
  styleUrl: './vertical-legends.component.scss',
})
export class VerticalLegendsComponent {
  @Input() chartType: string = SunburstChartType.DocumentTypes

  private readonly aiFacade = inject(AiFacade)

  public readonly legends = toSignal(
    combineLatest([
      this.aiFacade.selectEcaDocumentTypesSuccess$,
      this.aiFacade.selectEcaTopicsRelevantSuccess$,
      this.aiFacade.selectEcaTopicsNonRelevantSuccess$
    ]).pipe(
      map(([documentTypesData, relevantTopicsData, nonRelevantTopicsData]) => {
        switch (this.chartType) {
          case SunburstChartType.DocumentTypes:
            if (documentTypesData?.data?.documentTypes) {
              return documentTypesData.data.documentTypes.map((docType: any) => docType.docTypeName)
            }
            break
          case SunburstChartType.RelevantDocuments:
            if (relevantTopicsData?.data?.topics) {
              return relevantTopicsData.data.topics.map((topic: any) => topic.topicName)
            }
            break
          case SunburstChartType.NotRelevantDocuments:
            if (nonRelevantTopicsData?.data?.topics) {
              return nonRelevantTopicsData.data.topics.map((topic: any) => topic.topicName)
            }
            break
        }
        return []
      })
    ),
    { initialValue: [] }
  )

  public readonly chartColors: string[] = sunburstAnnotationColors
}
