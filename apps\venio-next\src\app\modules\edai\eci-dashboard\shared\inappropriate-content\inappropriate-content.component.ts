import { Component, inject } from '@angular/core'
import { PlotlyModule } from 'angular-plotly.js'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade } from '@venio/data-access/ai'
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'
import { hyperlinkOpenSmIcon, SVGIcon } from '@progress/kendo-svg-icons'

@Component({
  selector: 'venio-inappropriate-content',
  standalone: true,
  imports: [PlotlyModule, KENDO_BUTTONS, SvgLoaderDirective],
  templateUrl: './inappropriate-content.component.html',
  styleUrl: './inappropriate-content.component.scss',
})
export class InappropriateContentComponent {
  private readonly aiFacade = inject(AiFacade)

  public readonly svgOpenNew: SVGIcon = hyperlinkOpenSmIcon

  public readonly barChartData = toSignal(
    this.aiFacade.selectEciBarChartData$,
    { initialValue: null }
  )

  openFocusedSection() {
    this.aiFacade.setEciFocusedSectionOpened(true)
  }

  get graph() {
    const data = this.barChartData()
    const categories = data?.categories || []
    const values = data?.values || []

    return {
      data: [
        {
          x: categories,
          y: values,
          type: 'bar',
          marker: {
            color: '#6366f1',
          },
        },
      ],
      layout: {
        autosize: true,
        title: '',
        automargin: true,
        margin: { t: 20, r: 20, b: 60, l: 60 },
        showlegend: false,
        xaxis: {
          title: '',
          tickangle: -45,
        },
        yaxis: {
          title: 'Count',
        },
        plot_bgcolor: 'rgba(0,0,0,0)',
        paper_bgcolor: 'rgba(0,0,0,0)',
      },
    }
  }

  public config = {
    responsive: true,
    displayModeBar: false,
    displaylogo: false,
    modeBarButtonsToRemove: [
      'toImage',
      'sendDataToCloud',
      'editInChartStudio',
      'zoom2d',
      'select2d',
      'pan2d',
      'lasso2d',
      'autoScale2d',
      'resetScale2d',
    ],
  }
}
