import { Component, inject } from '@angular/core'
import { DecimalPipe } from '@angular/common'
import { KENDO_BUTTONS } from '@progress/kendo-angular-buttons'
import { toSignal } from '@angular/core/rxjs-interop'
import { map } from 'rxjs'
import { AiFacade } from '@venio/data-access/ai'
import { SvgLoaderDirective } from '@venio/feature/shared/directives'

@Component({
  selector: 'venio-summary',
  standalone: true,
  imports: [KENDO_BUTTONS, DecimalPipe, SvgLoaderDirective],
  templateUrl: './summary.component.html',
  styleUrl: './summary.component.scss',
})
export class SummaryComponent {
  private readonly aiFacade = inject(AiFacade)

  public readonly selectedDocuments = toSignal(
    this.aiFacade.selectEciSelectedDocuments$,
    { initialValue: 0 }
  )

  public readonly totalDocuments = toSignal(
    this.aiFacade.selectEciTotalDocuments$,
    { initialValue: 0 }
  )

  // ECA API data for calculating totals
  public readonly ecaRelevanceData = toSignal(
    this.aiFacade.selectEcaRelevanceSuccess$,
    { initialValue: null }
  )

  // Computed total from ECA relevance data
  public readonly computedTotalDocuments = toSignal(
    this.aiFacade.selectEcaRelevanceSuccess$.pipe(
      map((ecaData) => {
        if (ecaData?.data) {
          return ecaData.data.reduce((total: number, item: any) => total + item.docCount, 0)
        }
        return this.totalDocuments() || 0
      })
    ),
    { initialValue: 0 }
  )

  public onCaseButtonClick(): void {
    console.log('Case Info button clicked!')
  }

  public onNarrativeButtonClick(): void {
    console.log('Button clicked!')
  }
}
