import { Component, inject, OnInit } from '@angular/core'
import {
  TagCloudComponent,
  CloudOptions,
  CloudData,
} from 'angular-tag-cloud-module'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade } from '@venio/data-access/ai'
import { map } from 'rxjs'

@Component({
  selector: 'venio-word-cloud',
  standalone: true,
  imports: [TagCloudComponent, TitleAndDownloadComponent],
  templateUrl: './word-cloud.component.html',
  styleUrl: './word-cloud.component.scss',
})
export class WordCloudComponent implements OnInit {
  private readonly aiFacade = inject(AiFacade)

  public readonly options: CloudOptions = {
    // if width is between 0 and 1 it will be set to the width of the upper element multiplied by the value
    width: 1000,
    // if height is between 0 and 1 it will be set to the height of the upper element multiplied by the value
    height: 400,
    overflow: false,
  }

  // ECA API data
  public readonly ecaTopicsData = toSignal(
    this.aiFacade.selectEcaTopicsSuccess$,
    { initialValue: null }
  )

  public readonly data = toSignal(
    this.aiFacade.selectEciWordCloudData$.pipe(
      map((wordCloudData) => {
        // Use ECA API data if available
        const ecaData = this.ecaTopicsData()
        if (ecaData?.data?.topics) {
          return this.#transformEcaTopicsToWordCloud(ecaData.data.topics)
        }

        return wordCloudData.map(
          (item) =>
            ({
              text: item.text,
              weight: item.weight,
              color: item.color,
              tooltip: item.tooltip,
              link: item.link,
              external: item.external || false,
            } as CloudData)
        )
      })
    ),
    { initialValue: [] as CloudData[] }
  )

  ngOnInit() {
    // Component initialization
  }

  #transformEcaTopicsToWordCloud(ecaTopics: any[]): CloudData[] {
    // Transform ECA topics to word cloud format
    return ecaTopics.map((topic, index) => ({
      text: topic.topicName,
      weight: Math.max(10, Math.min(100, topic.docCount / 10)), // Scale weight between 10-100
      color: this.#generateColor(index),
      tooltip: `${topic.topicName}: ${topic.docCount} documents (${topic.percentage.toFixed(1)}%)`,
      link: '',
      external: false,
    } as CloudData))
  }

  #generateColor(index: number): string {
    const colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
      '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
    ]
    return colors[index % colors.length]
  }
}
