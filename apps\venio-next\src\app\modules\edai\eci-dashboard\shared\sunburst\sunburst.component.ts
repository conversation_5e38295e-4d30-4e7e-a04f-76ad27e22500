import { Component, OnInit, Input, inject } from '@angular/core'
import { PlotlyModule } from 'angular-plotly.js'
import { calculateSunburstData } from './helpers'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'
import { VerticalLegendsComponent } from '../vertical-legends/vertical-legends.component'
import { NgIf } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade } from '@venio/data-access/ai'
import {
  formatParentData,
  formatChildData,
} from '../data-table-for-focused-section/helpers'
import { CenterTextComponent } from '../center-text/center-text.component'

@Component({
  selector: 'venio-sunburst',
  standalone: true,
  imports: [
    PlotlyModule,
    TitleAndDownloadComponent,
    VerticalLegendsComponent,
    NgIf,
    CenterTextComponent,
  ],
  templateUrl: './sunburst.component.html',
  styleUrl: './sunburst.component.scss',
})
export class SunburstComponent implements OnInit {
  @Input() showLegend = true

  @Input() chartTitle = 'Document Types'

  private readonly aiFacade = inject(AiFacade)

  public readonly documentTypes = toSignal(
    this.aiFacade.selectEciSortedDocumentTypes$,
    { initialValue: [] }
  )

  // ECA API data
  public readonly ecaDocumentTypesData = toSignal(
    this.aiFacade.selectEcaDocumentTypesSuccess$,
    { initialValue: null }
  )

  public readonly isParentData = toSignal(
    this.aiFacade.selectEciIsParentData$,
    { initialValue: true }
  )

  public parentLabels: any

  public childrenLabels: any

  public chartOneTotal!: number

  public chartOneSubTotal!: number[]

  public chartOnePercents: number[] | undefined

  public chartOneChildPercents: number[][] | undefined

  public allCountsOne!: number[]

  public allValsOne!: number[]

  public formattedCounts!: string[]

  public config: any

  public graph: any

  public labels: string[] = []

  public parents: string[] = ['Total']

  ngOnInit() {
    // Initialize chart data when document types are available
    this.aiFacade.selectEciDocumentTypes$.subscribe((documentTypes) => {
      if (documentTypes.length > 0) {
        this.initializeChartData(documentTypes)
      }
    })

    // Also listen for ECA API data
    this.aiFacade.selectEcaDocumentTypesSuccess$.subscribe((ecaData) => {
      if (ecaData?.data?.documentTypes) {
        const transformedData = this.#transformEcaDocumentTypes(ecaData.data.documentTypes)
        this.initializeChartData(transformedData)
      }
    })
  }

  private initializeChartData(documentTypes: any[]) {
    const sortedDocuTypes = [...documentTypes].sort((a, b) => b.count - a.count)

    const {
      parentLabels,
      childrenLabels,
      chartOneTotal,
      chartOneSubTotal,
      chartOnePercents,
      chartOneChildPercents,
      allCountsOne,
      allValsOne,
      formattedCounts,
    } = calculateSunburstData(sortedDocuTypes)

    this.parentLabels = parentLabels
    this.childrenLabels = childrenLabels
    this.chartOneTotal = chartOneTotal
    this.chartOneSubTotal = chartOneSubTotal
    this.chartOnePercents = chartOnePercents
    this.chartOneChildPercents = chartOneChildPercents
    this.allCountsOne = allCountsOne
    this.allValsOne = allValsOne
    this.formattedCounts = formattedCounts

    this.labels = ['Total']
    this.parents = ['']

    const parentsLen = this.parentLabels.length
    this.labels.push(...this.parentLabels)
    this.parents.push(...Array.from({ length: parentsLen }, () => 'Total'))

    for (let i = 0; i < this.childrenLabels.length; i++) {
      this.labels.push(...this.childrenLabels[i])
      const len = this.childrenLabels[i].length
      this.parents.push(
        ...Array.from({ length: len }, () => this.parentLabels[i])
      )
    }

    // structure for the sunburst chart for angular - adjusting from React version
    const updatedVals = [...this.allValsOne]
    this.parents.forEach((parent, idx) => {
      if (parent === 'Total' || idx === 0) {
        updatedVals[idx] = 0
      }
    })

    this.graph = {
      data: [
        {
          type: 'sunburst',
          labels: this.labels,
          parents: this.parents,
          values: updatedVals,
          hole: 0.5,
          textinfo: 'label',
        },
      ],
      layout: {
        autosize: true,
        automargin: true,
        branchvalues: 'remainder', // Required for donut
        margin: { t: 0, r: 0, b: 0, l: 0 },
      },
    }
    this.config = {
      responsive: true,
      displayModeBar: false,
      displaylogo: false,
      modeBarButtonsToRemove: [
        'toImage',
        'sendDataToCloud',
        'editInChartStudio',
        'zoom2d',
        'select2d',
        'pan2d',
        'lasso2d',
        'autoScale2d',
        'resetScale2d',
      ],
    }
  }

  onChartClick(event: any) {
    const currentIsParentData = this.isParentData()
    const documentTypes = this.documentTypes()

    if (currentIsParentData && event.points[0].parent === 'Total') {
      // child is clicked
      const childLabel = event.points[0].label
      const childData = documentTypes.find(
        (docuType) => docuType.category === childLabel
      )
      console.log('Child data:', childData)
      if (!childData) return

      const formatted = formatChildData(childData)
      this.aiFacade.storeEciTableData(formatted)
      this.aiFacade.setEciParentDataView(false)
      this.aiFacade.setEciShowDetails(true)
    } else if (!currentIsParentData && event.points[0].parent === 'Total') {
      console.log('Clicked on parent -Total')
      const formatted = formatParentData(documentTypes)
      this.aiFacade.storeEciTableData(formatted)
      this.aiFacade.setEciParentDataView(true)
      this.aiFacade.setEciShowDetails(false)
    } else {
      console.log('grandchild clicked', event.points[0])
      // open details data table
      this.aiFacade.setEciShowDetails(true)
    }
  }

  #transformEcaDocumentTypes(ecaDocumentTypes: any[]): any[] {
    // Transform ECA document types to match the expected format
    return ecaDocumentTypes.map((docType) => ({
      id: docType.docTypeId,
      name: docType.docTypeName,
      count: docType.docCount,
      percentage: docType.percentage,
      category: docType.parentDocTypeId === 0 ? docType.docTypeName : this.#findParentName(ecaDocumentTypes, docType.parentDocTypeId),
      subcategory: docType.parentDocTypeId !== 0 ? docType.docTypeName : undefined,
      subcategories: docType.children?.map((child: any) => ({
        id: child.docTypeId,
        name: child.docTypeName,
        count: child.docCount,
        percentage: child.percentage,
      })) || []
    }))
  }

  #findParentName(docTypes: any[], parentId: number): string {
    const parent = docTypes.find(dt => dt.docTypeId === parentId)
    return parent?.docTypeName || 'Unknown'
  }
}
