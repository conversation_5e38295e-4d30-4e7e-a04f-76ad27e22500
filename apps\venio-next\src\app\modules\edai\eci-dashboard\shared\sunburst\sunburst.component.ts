import { Component, OnInit, Input, inject } from '@angular/core'
import { PlotlyModule } from 'angular-plotly.js'
import { calculateSunburstData } from './helpers'
import { TitleAndDownloadComponent } from '../title-and-download/title-and-download.component'
import { VerticalLegendsComponent } from '../vertical-legends/vertical-legends.component'
import { NgIf } from '@angular/common'
import { toSignal } from '@angular/core/rxjs-interop'
import { AiFacade, SunburstChartType } from '@venio/data-access/ai'
import {
  formatParentData,
  formatChildData,
} from '../data-table-for-focused-section/helpers'
import { CenterTextComponent } from '../center-text/center-text.component'

@Component({
  selector: 'venio-sunburst',
  standalone: true,
  imports: [
    PlotlyModule,
    TitleAndDownloadComponent,
    VerticalLegendsComponent,
    NgIf,
    CenterTextComponent,
  ],
  templateUrl: './sunburst.component.html',
  styleUrl: './sunburst.component.scss',
})
export class SunburstComponent implements OnInit {
  @Input() showLegend = true

  @Input() chartTitle = 'Document Types'

  private readonly aiFacade = inject(AiFacade)

  public readonly documentTypes = toSignal(
    this.aiFacade.selectEciSortedDocumentTypes$,
    { initialValue: [] }
  )

  // ECA API data - Document Types
  public readonly ecaDocumentTypesData = toSignal(
    this.aiFacade.selectEcaDocumentTypesSuccess$,
    { initialValue: null }
  )

  // ECA API data - Topics Relevant
  public readonly ecaTopicsRelevantData = toSignal(
    this.aiFacade.selectEcaTopicsRelevantSuccess$,
    { initialValue: null }
  )

  // ECA API data - Topics Non-Relevant
  public readonly ecaTopicsNonRelevantData = toSignal(
    this.aiFacade.selectEcaTopicsNonRelevantSuccess$,
    { initialValue: null }
  )

  public readonly isParentData = toSignal(
    this.aiFacade.selectEciIsParentData$,
    { initialValue: true }
  )

  public parentLabels: any

  public childrenLabels: any

  public chartOneTotal!: number

  public chartOneSubTotal!: number[]

  public chartOnePercents: number[] | undefined

  public chartOneChildPercents: number[][] | undefined

  public allCountsOne!: number[]

  public allValsOne!: number[]

  public formattedCounts!: string[]

  public config: any

  public graph: any

  public labels: string[] = []

  public parents: string[] = ['Total']

  ngOnInit() {
    // Initialize chart data based on chart type
    this.#initializeDataSubscriptions()
  }

  #initializeDataSubscriptions() {
    switch (this.chartTitle) {
      case SunburstChartType.DocumentTypes:
        this.#subscribeToDocumentTypesData()
        break
      case SunburstChartType.RelevantDocuments:
        this.#subscribeToRelevantTopicsData()
        break
      case SunburstChartType.NotRelevantDocuments:
        this.#subscribeToNonRelevantTopicsData()
        break
      default:
        // Fallback to document types
        this.#subscribeToDocumentTypesData()
        break
    }
  }

  #subscribeToDocumentTypesData() {
    // Listen for ECA Document Types API data
    this.aiFacade.selectEcaDocumentTypesSuccess$.subscribe((ecaData) => {
      if (ecaData?.data?.documentTypes) {
        const transformedData = this.#transformEcaDocumentTypes(ecaData.data.documentTypes)
        this.initializeChartData(transformedData)
      }
    })

    // Fallback to legacy data if ECA data not available
    this.aiFacade.selectEciDocumentTypes$.subscribe((documentTypes) => {
      if (documentTypes.length > 0 && !this.ecaDocumentTypesData()) {
        this.initializeChartData(documentTypes)
      }
    })
  }

  #subscribeToRelevantTopicsData() {
    // Listen for ECA Topics Relevant API data
    this.aiFacade.selectEcaTopicsRelevantSuccess$.subscribe((ecaData) => {
      if (ecaData?.data?.topics) {
        const transformedData = this.#transformEcaTopicsToDocumentTypes(ecaData.data.topics)
        this.initializeChartData(transformedData)
      }
    })
  }

  #subscribeToNonRelevantTopicsData() {
    // Listen for ECA Topics Non-Relevant API data
    this.aiFacade.selectEcaTopicsNonRelevantSuccess$.subscribe((ecaData) => {
      if (ecaData?.data?.topics) {
        const transformedData = this.#transformEcaTopicsToDocumentTypes(ecaData.data.topics)
        this.initializeChartData(transformedData)
      }
    })
  }

  private initializeChartData(documentTypes: any[]) {
    const sortedDocuTypes = [...documentTypes].sort((a, b) => b.count - a.count)

    const {
      parentLabels,
      childrenLabels,
      chartOneTotal,
      chartOneSubTotal,
      chartOnePercents,
      chartOneChildPercents,
      allCountsOne,
      allValsOne,
      formattedCounts,
    } = calculateSunburstData(sortedDocuTypes)

    this.parentLabels = parentLabels
    this.childrenLabels = childrenLabels
    this.chartOneTotal = chartOneTotal
    this.chartOneSubTotal = chartOneSubTotal
    this.chartOnePercents = chartOnePercents
    this.chartOneChildPercents = chartOneChildPercents
    this.allCountsOne = allCountsOne
    this.allValsOne = allValsOne
    this.formattedCounts = formattedCounts

    this.labels = ['Total']
    this.parents = ['']

    const parentsLen = this.parentLabels.length
    this.labels.push(...this.parentLabels)
    this.parents.push(...Array.from({ length: parentsLen }, () => 'Total'))

    for (let i = 0; i < this.childrenLabels.length; i++) {
      this.labels.push(...this.childrenLabels[i])
      const len = this.childrenLabels[i].length
      this.parents.push(
        ...Array.from({ length: len }, () => this.parentLabels[i])
      )
    }

    // structure for the sunburst chart for angular - adjusting from React version
    const updatedVals = [...this.allValsOne]
    this.parents.forEach((parent, idx) => {
      if (parent === 'Total' || idx === 0) {
        updatedVals[idx] = 0
      }
    })

    this.graph = {
      data: [
        {
          type: 'sunburst',
          labels: this.labels,
          parents: this.parents,
          values: updatedVals,
          hole: 0.5,
          textinfo: 'label',
        },
      ],
      layout: {
        autosize: true,
        automargin: true,
        branchvalues: 'remainder', // Required for donut
        margin: { t: 0, r: 0, b: 0, l: 0 },
      },
    }
    this.config = {
      responsive: true,
      displayModeBar: false,
      displaylogo: false,
      modeBarButtonsToRemove: [
        'toImage',
        'sendDataToCloud',
        'editInChartStudio',
        'zoom2d',
        'select2d',
        'pan2d',
        'lasso2d',
        'autoScale2d',
        'resetScale2d',
      ],
    }
  }

  onChartClick(event: any) {
    const currentIsParentData = this.isParentData()

    // Get the appropriate data based on chart type
    const chartData = this.#getCurrentChartData()

    if (currentIsParentData && event.points[0].parent === 'Total') {
      // Parent segment clicked - show child data in table
      const childLabel = event.points[0].label
      const childData = chartData.find(
        (item: any) => item.category === childLabel || item.name === childLabel
      )

      console.log(`${this.chartTitle} - Child data clicked:`, childData)
      if (!childData) return

      const formatted = formatChildData(childData)
      this.aiFacade.storeEciTableData(formatted)
      this.aiFacade.setEciParentDataView(false)
      this.aiFacade.setEciShowDetails(true)

      // Open focused section to show the detailed view
      this.aiFacade.setEciFocusedSectionOpened(true)

    } else if (!currentIsParentData && event.points[0].parent === 'Total') {
      // Back to parent view
      console.log(`${this.chartTitle} - Back to parent view`)
      const formatted = formatParentData(chartData)
      this.aiFacade.storeEciTableData(formatted)
      this.aiFacade.setEciParentDataView(true)
      this.aiFacade.setEciShowDetails(false)

    } else {
      // Grandchild clicked - show file details
      console.log(`${this.chartTitle} - Grandchild clicked:`, event.points[0])
      this.aiFacade.setEciShowDetails(true)
      this.aiFacade.setEciFocusedSectionOpened(true)
    }
  }

  #getCurrentChartData(): any[] {
    switch (this.chartTitle) {
      case SunburstChartType.DocumentTypes:
        const docTypesData = this.ecaDocumentTypesData()
        if (docTypesData?.data?.documentTypes) {
          return this.#transformEcaDocumentTypes(docTypesData.data.documentTypes)
        }
        return this.documentTypes()

      case SunburstChartType.RelevantDocuments:
        const relevantData = this.ecaTopicsRelevantData()
        if (relevantData?.data?.topics) {
          return this.#transformEcaTopicsToDocumentTypes(relevantData.data.topics)
        }
        return []

      case SunburstChartType.NotRelevantDocuments:
        const nonRelevantData = this.ecaTopicsNonRelevantData()
        if (nonRelevantData?.data?.topics) {
          return this.#transformEcaTopicsToDocumentTypes(nonRelevantData.data.topics)
        }
        return []

      default:
        return this.documentTypes()
    }
  }

  #transformEcaDocumentTypes(ecaDocumentTypes: any[]): any[] {
    // Transform ECA document types to match the expected format
    return ecaDocumentTypes.map((docType) => ({
      id: docType.docTypeId,
      name: docType.docTypeName,
      count: docType.docCount,
      percentage: docType.percentage,
      category: docType.docTypeName,
      subcategories: docType.children?.map((child: any) => ({
        subcategory: child.docTypeName,
        count: child.docCount,
        percentage: child.percentage,
      })) || []
    }))
  }

  #transformEcaTopicsToDocumentTypes(ecaTopics: any[]): any[] {
    // Transform ECA topics to match the document types format for sunburst chart
    return ecaTopics.map((topic) => ({
      id: topic.topicId,
      name: topic.topicName,
      count: topic.docCount,
      percentage: topic.percentage,
      category: topic.topicName,
      subcategories: topic.children?.map((child: any) => ({
        subcategory: child.topicName,
        count: child.docCount,
        percentage: child.percentage,
      })) || []
    }))
  }

  #findParentName(docTypes: any[], parentId: number): string {
    const parent = docTypes.find(dt => dt.docTypeId === parentId)
    return parent?.docTypeName || 'Unknown'
  }
}
