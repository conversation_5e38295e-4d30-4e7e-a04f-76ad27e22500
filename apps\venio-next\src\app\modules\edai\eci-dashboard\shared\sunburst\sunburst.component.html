<div class="t-bg-white t-border t-flex t-gap-6 t-p-7 t-rounded t-flex-col sunburst-wrapper"
  style="border-color: #dcdcdc">
  <venio-title-and-download [title]="chartTitle"></venio-title-and-download>
  <div class="t-w-full t-min-h-[400px] t-relative">
    <venio-center-text [centerText]="chartTitle" [showViewDetails]="true"></venio-center-text>
    <plotly-plot [data]="graph.data" [layout]="graph.layout" [config]="config"
      (plotlyClick)="onChartClick($event)"></plotly-plot>
  </div>
  <venio-vertical-legends *ngIf="showLegend" [chartType]="chartTitle"></venio-vertical-legends>
  <div class="t-mb-2"></div>
</div>